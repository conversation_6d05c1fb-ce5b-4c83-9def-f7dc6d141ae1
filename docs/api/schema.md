---
search:
  boost: 2 
---


## Schema and field
Schemas, fields, and data types are provided in the ``deltalake.schema`` submodule.

::: deltalake.Schema
    options:
        show_root_heading: true
        show_root_toc_entry: true

::: deltalake.Field
    options:
        show_root_heading: true
        show_root_toc_entry: true


## Data types
::: deltalake.schema.PrimitiveType
    options:
        show_root_heading: true
        show_root_toc_entry: true

::: deltalake.schema.ArrayType
    options:
        show_root_heading: true
        show_root_toc_entry: true

::: deltalake.schema.MapType
    options:
        show_root_heading: true
        show_root_toc_entry: true

::: deltalake.schema.StructType
    options:
        show_root_heading: true
        show_root_toc_entry: true