# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  - package-ecosystem: "cargo"
    directory: "/"
    schedule:
      interval: "weekly"
    ignore:
      # arrow and datafusion are bumped manually
      - dependency-name: "arrow*"
        update-types: ["version-update:semver-major"]
      - dependency-name: "datafusion*"
        update-types: ["version-update:semver-major"]
