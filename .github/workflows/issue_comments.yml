name: Comment Commands
on:
  issue_comment:
    types: created

permissions:
  issues: write

jobs:
  issue_assign:
    runs-on: ubuntu-latest
    if: (!github.event.issue.pull_request) && github.event.comment.body == 'take'
    concurrency:
      # Only run one a time per user
      group: ${{ github.actor }}-issue-assign
    steps:
      - run: |
          CODE=$(curl -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" -LI https://api.github.com/repos/${{ github.repository }}/issues/${{ github.event.issue.number }}/assignees/${{ github.event.comment.user.login }} -o /dev/null -w '%{http_code}\n' -s)
          if [ "$CODE" -eq "204" ]
          then
            echo "Assigning issue ${{ github.event.issue.number }} to ${{ github.event.comment.user.login }}"
            curl -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" -d '{"assignees": ["${{ github.event.comment.user.login }}"]}' https://api.github.com/repos/${{ github.repository }}/issues/${{ github.event.issue.number }}/assignees
          else
            echo "Issue ${{ github.event.issue.number }} cannot be assigned to ${{ github.event.comment.user.login }}"
          fi