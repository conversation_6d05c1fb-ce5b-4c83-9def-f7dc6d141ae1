name: dev_pr

# Trigger whenever a PR is changed (title as well as new / changed commits)
on:
  pull_request_target:
    types:
      - opened
      - edited
      - synchronize
      - reopened

jobs:
  process:
    name: Process
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
    steps:
      - uses: actions/checkout@v3

      - name: Assign GitHub labels
        if: |
          github.event_name == 'pull_request_target' &&
            (github.event.action == 'opened' ||
             github.event.action == 'synchronize')
        uses: actions/labeler@v4.0.2
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          configuration-path: .github/workflows/dev_pr/labeler.yml
          sync-labels: true

  commitlint:
    name: PR title / description conforms to semantic-release
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    steps:
      - uses: actions/setup-node@v3
        with:
          node-version: "18"
      - run: npm install -g @commitlint/cli @commitlint/config-conventional
      # Checkout to get the commitlint configuration.
      - uses: actions/checkout@v3
      - run: >
          npx commitlint \
            --verbose <<< $COMMIT_MSG
        env:
          COMMIT_MSG: >
            ${{ github.event.pull_request.title }}


            ${{ github.event.pull_request.body }}
      - if: failure()
        uses: actions/github-script@v6
        with:
          script: |
            const message = `**ACTION NEEDED**
              
              delta-rs follows the [Conventional Commits\
              specification](https://www.conventionalcommits.org/en/v1.0.0/) for\
              release automation.

              The PR title and description are used as the merge commit message.\
              Please update your PR title and description to match the specification.
              `
            // Get list of current comments
            const comments = await github.paginate(github.rest.issues.listComments, {
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number
            });
            // Check if this job already commented
            for (const comment of comments) {
              if (comment.body === message) {
                return // Already commented
              }
            }
            // Post the comment about Conventional Commits
            github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: message
            })
            core.setFailed(message)
