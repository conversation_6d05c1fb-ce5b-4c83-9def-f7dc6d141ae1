name: Release to Cargo

on:
  push:
    tags: ["rust-v*"]

defaults:
  run:
    working-directory: ./rust

jobs:
  validate-release-tag:
    name: Validate git tag
    runs-on: ubuntu-20.04
    steps:
      - uses: actions/checkout@v3
      - name: compare git tag with cargo metadata
        run: |
          PUSHED_TAG=${GITHUB_REF##*/}
          CURR_VER=$( grep version Cargo.toml | head -n 1 | awk '{print $3}' | tr -d '"' )
          if [[ "${PUSHED_TAG}" != "rust-v${CURR_VER}" ]]; then
            echo "Cargo metadata has version set to ${CURR_VER}, but got pushed tag ${PUSHED_TAG}."
            exit 1
          fi

  release-crate:
    needs: validate-release-tag
    name: Release crate
    strategy:
      fail-fast: false
    runs-on: ubuntu-20.04
    steps:
      - uses: actions/checkout@v3

      - uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true

      - name: cargo publish
        uses: actions-rs/cargo@v1
        env:
          CARGO_REGISTRY_TOKEN: ${{ secrets.CARGO_REGISTRY_TOKEN }}
        with:
          command: publish
          args: --token "${CARGO_REGISTRY_TOKEN}" --manifest-path ./rust/Cargo.toml
