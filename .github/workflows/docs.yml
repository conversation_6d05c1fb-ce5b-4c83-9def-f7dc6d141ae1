name: Build (and maybe release) the documentation

on:
  pull_request:
    paths:
      - python/**
      - docs/**
      - mkdocs.yml
      - .github/workflows/docs.yml
  repository_dispatch:
    types:
      - release-docs
  # Allow manual trigger for now
  workflow_dispatch:

env:    
  IS_RELEASE: ${{ github.event_name == 'repository_dispatch' || github.event_name == 'workflow_dispatch' }}
  BUILD_ARGS: ${{ (github.event_name == 'repository_dispatch' || github.event_name == 'workflow_dispatch') && 'install MATURIN_EXTRA_ARGS="--manylinux off"' || 'develop' }}

jobs:
  markdown-link-check:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: gaurav-nelson/github-action-markdown-link-check@v1
      with:
        config-file: docs/mlc-config.json
        folder-path: docs

  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: |
          cd docs
          make check

  build-deploy:
    needs:
      [
        lint,
        markdown-link-check,
      ]
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v3

      - name: Setup Environment
        uses: ./.github/actions/setup-env

      - name: Build and install deltalake
        run: |
          cd python
          python -m venv venv
          source venv/bin/activate
          make ${{ env.BUILD_ARGS }}

      - name: Install dependencies
        run: |
          source python/venv/bin/activate
          pip install -r docs/requirements.txt

      - name: Build
        run: |
          source python/venv/bin/activate
          mkdocs build

      - name: Deploy
        if: ${{ env.IS_RELEASE == 'true' }}
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          folder: site
          clean-exclude: |
            python/
            .nojekyll
          single-commit: false
          git-config-name: "Github Action"
          git-config-email: "<EMAIL>"
          commit-message: "doc update for tag `${{ github.event.client_payload.tag || github.ref_name }}`"


