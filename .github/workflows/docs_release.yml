name: Release documentation

on:
  pull_request:
    types:
      - closed
    branches: [main]
    paths:
      - docs/**
      - mkdocs.yml

jobs:
  release-docs:
    if: github.event.pull_request.merged == true
    permissions:
      contents: write
    runs-on: ubuntu-latest
    steps:
      - name: Trigger the docs release event
        uses: peter-evans/repository-dispatch@v2
        with:
          event-type: release-docs
          client-payload: >
            {
              "tag": "${{ github.ref_name }}"
            }
