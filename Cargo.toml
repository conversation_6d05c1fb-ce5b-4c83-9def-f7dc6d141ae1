[workspace]
members = ["crates/*", "delta-inspect", "python"]
exclude = ["proofs"]
resolver = "2"

[workspace.package]
authors = ["Qingping Ho<PERSON> <<EMAIL>>"]
rust-version = "1.75"
keywords = ["deltalake", "delta", "datalake"]
readme = "README.md"
edition = "2021"
description = "Native Delta Lake implementation in Rust"
homepage = "https://github.com/delta-io/delta.rs"
license = "Apache-2.0"
documentation = "https://docs.rs/deltalake"
repository = "https://github.com/delta-io/delta.rs"

[profile.release-with-debug]
inherits = "release"
debug = true

# Reducing the debuginfo for the test profile in order to trim the disk and RAM
# usage during development
# <https://github.com/delta-io/delta-rs/issues/1550?
[profile.test]
debug = "line-tables-only"

[workspace.dependencies]
delta_kernel = { version = "0.2.0" }
# delta_kernel = { path = "../delta-kernel-rs/kernel" }

# arrow
arrow = { version = "52" }
arrow-arith = { version = "52" }
arrow-array = { version = "52", features = ["chrono-tz"] }
arrow-buffer = { version = "52" }
arrow-cast = { version = "52" }
arrow-ipc = { version = "52" }
arrow-json = { version = "52" }
arrow-ord = { version = "52" }
arrow-row = { version = "52" }
arrow-schema = { version = "52" }
arrow-select = { version = "52" }
object_store = { version = "0.10.1" }
parquet = { version = "52" }

# datafusion
datafusion = { version = "39" }
datafusion-expr = { version = "39" }
datafusion-common = { version = "39" }
datafusion-proto = { version = "39" }
datafusion-sql = { version = "39" }
datafusion-physical-expr = { version = "39" }
datafusion-functions = { version = "39" }
datafusion-functions-array = { version = "39" }

# serde
serde = { version = "1.0.194", features = ["derive"] }
serde_json = "1"

# "stdlib"
bytes = { version = "1" }
chrono = { version = ">0.4.34", default-features = false, features = ["clock"] }
tracing = { version = "0.1", features = ["log"] }
regex = { version = "1" }
thiserror = { version = "1" }
url = { version = "2" }
uuid = { version = "1" }

# runtime / async
async-trait = { version = "0.1" }
futures = { version = "0.3" }
tokio = { version = "1" }
num_cpus = { version = "1" }
