/target
**/target
*.sw*
tlaplus/*.toolbox/*_SnapShot_*/
tlaplus/*.toolbox/*_SnapShot_*.launch
tlaplus/*.toolbox/*.tla.pmap
tlaplus/*.toolbox/*/*.out
tlaplus/*.toolbox/*/*.tla
tlaplus/*.toolbox/*/MC.cfg
tlaplus/*.toolbox/*/[0-9]*-[0-9]*-[0-9]*-[0-9]*-[0-9]*-[0-9]*/
/.idea
.vscode
.env
.venv
venv
**/.DS_Store
**/.python-version
.coverage
*.env
__azurite*
__blobstorage__
.githubchangeloggenerator.cache.log
.githubchangeloggenerator.cache/
.githubchangeloggenerator*
data

# Add all Cargo.lock files except for those in binary crates
Cargo.lock
!/aws/delta-checkpoint/Cargo.lock
!/delta-inspect/Cargo.lock
!/proofs/Cargo.lock

justfile
site
__pycache__