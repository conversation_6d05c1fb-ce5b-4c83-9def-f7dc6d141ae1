from datetime import date, datetime

from deltalake import DeltaTable


def test_read_cdf_partitioned():
    dt = DeltaTable("../crates/test/tests/data/cdf-table/")
    b = dt.load_cdf(0, 3).read_all().to_pydict()
    assert sorted(b["id"]) == [
        1,
        2,
        2,
        2,
        3,
        3,
        3,
        4,
        4,
        4,
        5,
        5,
        5,
        6,
        6,
        6,
        7,
        7,
        7,
        7,
        8,
        9,
        10,
    ]
    assert sorted(b["name"]) == [
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
    ]
    assert sorted(b["_change_type"]) == [
        "delete",
        "insert",
        "insert",
        "insert",
        "insert",
        "insert",
        "insert",
        "insert",
        "insert",
        "insert",
        "insert",
        "update_postimage",
        "update_postimage",
        "update_postimage",
        "update_postimage",
        "update_postimage",
        "update_postimage",
        "update_preimage",
        "update_preimage",
        "update_preimage",
        "update_preimage",
        "update_preimage",
        "update_preimage",
    ]
    assert sorted(b["_commit_version"]) == [
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        1,
        1,
        1,
        1,
        1,
        1,
        2,
        2,
        2,
        2,
        2,
        2,
        3,
    ]
    assert sorted(b["_commit_timestamp"]) == [
        datetime(2023, 12, 22, 17, 10, 18, 828000),
        datetime(2023, 12, 22, 17, 10, 18, 828000),
        datetime(2023, 12, 22, 17, 10, 18, 828000),
        datetime(2023, 12, 22, 17, 10, 18, 828000),
        datetime(2023, 12, 22, 17, 10, 18, 828000),
        datetime(2023, 12, 22, 17, 10, 18, 828000),
        datetime(2023, 12, 22, 17, 10, 18, 828000),
        datetime(2023, 12, 22, 17, 10, 18, 828000),
        datetime(2023, 12, 22, 17, 10, 18, 828000),
        datetime(2023, 12, 22, 17, 10, 18, 828000),
        datetime(2023, 12, 22, 17, 10, 21, 675000),
        datetime(2023, 12, 22, 17, 10, 21, 675000),
        datetime(2023, 12, 22, 17, 10, 21, 675000),
        datetime(2023, 12, 22, 17, 10, 21, 675000),
        datetime(2023, 12, 22, 17, 10, 21, 675000),
        datetime(2023, 12, 22, 17, 10, 21, 675000),
        datetime(2023, 12, 29, 21, 41, 33, 785000),
        datetime(2023, 12, 29, 21, 41, 33, 785000),
        datetime(2023, 12, 29, 21, 41, 33, 785000),
        datetime(2023, 12, 29, 21, 41, 33, 785000),
        datetime(2023, 12, 29, 21, 41, 33, 785000),
        datetime(2023, 12, 29, 21, 41, 33, 785000),
        datetime(2024, 1, 6, 16, 44, 59, 570000),
    ]
    assert sorted(b["birthday"]) == [
        date(2023, 12, 22),
        date(2023, 12, 22),
        date(2023, 12, 22),
        date(2023, 12, 22),
        date(2023, 12, 23),
        date(2023, 12, 23),
        date(2023, 12, 23),
        date(2023, 12, 23),
        date(2023, 12, 23),
        date(2023, 12, 23),
        date(2023, 12, 24),
        date(2023, 12, 24),
        date(2023, 12, 24),
        date(2023, 12, 24),
        date(2023, 12, 24),
        date(2023, 12, 24),
        date(2023, 12, 25),
        date(2023, 12, 25),
        date(2023, 12, 25),
        date(2023, 12, 29),
        date(2023, 12, 29),
        date(2023, 12, 29),
        date(2023, 12, 29),
    ]


def test_read_cdf_non_partitioned():
    dt = DeltaTable("../crates/test/tests/data/cdf-table-non-partitioned/")
    b = dt.load_cdf(0, 3).read_all().to_pydict()

    assert sorted(b["id"]) == [
        1,
        2,
        2,
        2,
        3,
        3,
        3,
        4,
        4,
        4,
        5,
        5,
        5,
        6,
        6,
        6,
        7,
        7,
        7,
        7,
        8,
        9,
        10,
    ]
    assert sorted(b["name"]) == [
        "Ada",
        "Bob",
        "Bob",
        "Bob",
        "Borb",
        "Carl",
        "Carl",
        "Carl",
        "Claire",
        "Dave",
        "Dave",
        "Dave",
        "Dennis",
        "Dennis",
        "Dennis",
        "Dennis",
        "Emily",
        "Emily",
        "Emily",
        "Kate",
        "Kate",
        "Kate",
        "Steve",
    ]
    assert sorted(b["birthday"]) == [
        date(2024, 4, 14),
        date(2024, 4, 14),
        date(2024, 4, 14),
        date(2024, 4, 14),
        date(2024, 4, 14),
        date(2024, 4, 14),
        date(2024, 4, 14),
        date(2024, 4, 14),
        date(2024, 4, 15),
        date(2024, 4, 15),
        date(2024, 4, 15),
        date(2024, 4, 15),
        date(2024, 4, 15),
        date(2024, 4, 15),
        date(2024, 4, 16),
        date(2024, 4, 16),
        date(2024, 4, 16),
        date(2024, 4, 16),
        date(2024, 4, 16),
        date(2024, 4, 16),
        date(2024, 4, 17),
        date(2024, 4, 17),
        date(2024, 4, 17),
    ]
    assert sorted(b["long_field"]) == [
        1,
        1,
        1,
        1,
        2,
        2,
        2,
        3,
        3,
        3,
        4,
        4,
        4,
        5,
        5,
        5,
        6,
        6,
        6,
        6,
        7,
        8,
        99999999999999999,
    ]
    assert sorted(b["boolean_field"]) == [
        True,
        True,
        True,
        True,
        True,
        True,
        True,
        True,
        True,
        True,
        True,
        True,
        True,
        True,
        True,
        True,
        True,
        True,
        True,
        True,
        True,
        True,
        True,
    ]
    assert sorted(b["double_field"]) == [
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
        3.14,
    ]
    assert sorted(b["smallint_field"]) == [
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
    ]
    assert sorted(b["_change_type"]) == [
        "delete",
        "insert",
        "insert",
        "insert",
        "insert",
        "insert",
        "insert",
        "insert",
        "insert",
        "insert",
        "insert",
        "update_postimage",
        "update_postimage",
        "update_postimage",
        "update_postimage",
        "update_postimage",
        "update_postimage",
        "update_preimage",
        "update_preimage",
        "update_preimage",
        "update_preimage",
        "update_preimage",
        "update_preimage",
    ]
    assert sorted(b["_commit_version"]) == [
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        1,
        1,
        1,
        1,
        1,
        1,
        2,
        2,
        2,
        2,
        2,
        2,
        3,
    ]
    assert sorted(b["_commit_timestamp"]) == [
        datetime(2024, 4, 14, 15, 58, 26, 249000),
        datetime(2024, 4, 14, 15, 58, 26, 249000),
        datetime(2024, 4, 14, 15, 58, 26, 249000),
        datetime(2024, 4, 14, 15, 58, 26, 249000),
        datetime(2024, 4, 14, 15, 58, 26, 249000),
        datetime(2024, 4, 14, 15, 58, 26, 249000),
        datetime(2024, 4, 14, 15, 58, 26, 249000),
        datetime(2024, 4, 14, 15, 58, 26, 249000),
        datetime(2024, 4, 14, 15, 58, 26, 249000),
        datetime(2024, 4, 14, 15, 58, 26, 249000),
        datetime(2024, 4, 14, 15, 58, 29, 393000),
        datetime(2024, 4, 14, 15, 58, 29, 393000),
        datetime(2024, 4, 14, 15, 58, 29, 393000),
        datetime(2024, 4, 14, 15, 58, 29, 393000),
        datetime(2024, 4, 14, 15, 58, 29, 393000),
        datetime(2024, 4, 14, 15, 58, 29, 393000),
        datetime(2024, 4, 14, 15, 58, 31, 257000),
        datetime(2024, 4, 14, 15, 58, 31, 257000),
        datetime(2024, 4, 14, 15, 58, 31, 257000),
        datetime(2024, 4, 14, 15, 58, 31, 257000),
        datetime(2024, 4, 14, 15, 58, 31, 257000),
        datetime(2024, 4, 14, 15, 58, 31, 257000),
        datetime(2024, 4, 14, 15, 58, 32, 495000),
    ]
