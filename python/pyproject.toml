[build-system]
requires = ["maturin>=1,<2"]
build-backend = "maturin"

[project]
name = "deltalake"
description = "Native Delta Lake Python binding based on delta-rs with Pandas integration"
readme = "README.md"
license = {file = "licenses/deltalake_license.txt"}
requires-python = ">=3.8"
keywords = ["deltalake", "delta", "datalake", "pandas", "arrow"]
classifiers = [
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12"
]
dependencies = [
    "pyarrow>=8",
    "pyarrow-hotfix",    
]

[project.optional-dependencies]
pandas = [
    "pandas"
]
devel = [
    "azure-storage-blob==12.20.0",
    "packaging>=20",
    "pytest",
    "pytest-mock",
    "pytest-cov",
    "pytest-timeout",
    "sphinx<=4.5",
    "sphinx-rtd-theme",
    "toml",
    "wheel",
    "pytest-benchmark",
    # keep ruff and mypy versions in sync with .github/workflows/python_build.yml
    "mypy==1.10.1",
    "ruff==0.5.2"
]
pyspark = [
    "pyspark",
    "delta-spark",
    "numpy==1.22.2" # pyspark is not compatible with latest numpy
]

[project.urls]
documentation = "https://delta-io.github.io/delta-rs/"
repository = "https://github.com/delta-io/delta-rs/tree/main/python/"

[tool.maturin]
module-name = "deltalake._internal"

[tool.mypy]
files = "deltalake/*.py"
exclude = "^tests"
mypy_path = "./stubs"
disallow_any_generics = true
disallow_subclassing_any = true
disallow_untyped_calls = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = false
implicit_reexport = true
strict_equality = true

[tool.ruff.lint]
select = [
    # pycodestyle error
    "E",
    # pyflakes
    "F",
    # isort
    "I",
    # ruff-specific rules
    "RUF"
]
ignore = ["E501"]

[tool.ruff.lint.isort]
known-first-party = ["deltalake"]

[tool.pytest.ini_options]
addopts = "-v -m 'not integration and not benchmark'"
testpaths = [
    "tests",
    "deltalake",
]
markers = [
    "integration: marks tests as integration tests (deselect with '-m \"not integration\"')",
    "s3: marks tests as integration tests with S3 (deselect with '-m \"not s3\"')",
    "azure: marks tests as integration tests with Azure Blob Store",
    "pandas: marks tests that require pandas",
    "pyspark: marks tests that require pyspark",
]

[tool.coverage.run]
branch = true
source = ["deltalake"]
