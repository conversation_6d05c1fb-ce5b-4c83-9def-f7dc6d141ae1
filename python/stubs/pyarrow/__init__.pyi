from typing import Any, Callable

__version__: str
Schema: Any
Table: Any
RecordBatch: Any
RecordBatchReader: Any
Field: Any
DataType: Any
ListType: Any
StructType: Any
MapType: Any
FixedSizeListType: Any
FixedSizeBinaryType: Any
schema: Any
map_: Any
list_: Any
field: Any
struct: Any
type_for_alias: Any
date32: Any
date64: Any
decimal128: Any
int8: Any
int16: Any
int32: Any
int64: Any
uint8: Any
uint16: Any
uint32: Any
uint64: Any
float16: Any
float32: Any
float64: Any
large_string: Any
string: Any
large_binary: Any
binary: Any
large_list: Any
LargeListType: Any
dictionary: Any
timestamp: Any
TimestampType: Any

py_buffer: Callable[[bytes], Any]
NativeFile: Any
BufferReader: Any
PythonFile: Any
