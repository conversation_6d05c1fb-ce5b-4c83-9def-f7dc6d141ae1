[package]
name = "delta-inspect"
version = "0.8.0"
authors = ["<PERSON><PERSON> Hou <<EMAIL>>"]
homepage = "https://github.com/delta-io/delta.rs"
license = "Apache-2.0"
keywords = ["deltalake", "delta", "datalake"]
description = "Native Delta Lake commandline util"
edition = "2021"

[dependencies]
anyhow = "1"
chrono = { workspace = true, default-features = false, features = ["clock"] }
clap = { version = "3", features = ["color"] }
tokio = { version = "1", features = ["fs", "macros", "rt", "io-util"] }
env_logger = "0"

[dependencies.deltalake]
path = "../crates/deltalake"
version = "0"
features = ["azure", "gcs"]

[features]
default = ["rustls"]
native-tls = ["deltalake/s3-native-tls", "deltalake/glue"]
rustls = ["deltalake/s3", "deltalake/glue"]
