<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<launchConfiguration type="org.lamport.tla.toolbox.tool.tlc.modelCheck">
    <stringAttribute key="TLCCmdLineParameters" value=""/>
    <intAttribute key="collectCoverage" value="1"/>
    <stringAttribute key="configurationName" value="fast_dev"/>
    <booleanAttribute key="deferLiveness" value="true"/>
    <intAttribute key="dfidDepth" value="100"/>
    <booleanAttribute key="dfidMode" value="false"/>
    <intAttribute key="distributedFPSetCount" value="0"/>
    <stringAttribute key="distributedNetworkInterface" value="192.168.86.239"/>
    <intAttribute key="distributedNodesCount" value="1"/>
    <stringAttribute key="distributedTLC" value="off"/>
    <stringAttribute key="distributedTLCVMArgs" value=""/>
    <intAttribute key="fpBits" value="1"/>
    <intAttribute key="fpIndex" value="89"/>
    <booleanAttribute key="fpIndexRandom" value="true"/>
    <intAttribute key="maxHeapSize" value="86"/>
    <intAttribute key="maxSetSize" value="1000000"/>
    <booleanAttribute key="mcMode" value="true"/>
    <stringAttribute key="modelBehaviorInit" value=""/>
    <stringAttribute key="modelBehaviorNext" value=""/>
    <stringAttribute key="modelBehaviorSpec" value="Spec"/>
    <intAttribute key="modelBehaviorSpecType" value="1"/>
    <stringAttribute key="modelBehaviorVars" value="owner_, record_version_, local_record_version, re_upsert_err, cached_lock_lookup_time_, lock_owner, pc, local_counter, next_time_tick, active_writer, lock_released, record_version, lock_record_version, time_now, cached_lock_duration_, cached_lock_version, shared_counter, cached_lock_duration, local_count_value, stack, owner, cached_lock_lookup_time, lock_duration"/>
    <stringAttribute key="modelComments" value=""/>
    <booleanAttribute key="modelCorrectnessCheckDeadlock" value="true"/>
    <listAttribute key="modelCorrectnessInvariants">
        <listEntry value="1ConsistentCount"/>
    </listAttribute>
    <listAttribute key="modelCorrectnessProperties">
        <listEntry value="1Termination"/>
        <listEntry value="0NoMissCount"/>
    </listAttribute>
    <intAttribute key="modelEditorOpenTabs" value="0"/>
    <stringAttribute key="modelExpressionEval" value=""/>
    <listAttribute key="modelParameterConstants">
        <listEntry value="NULL;;NULL;1;0"/>
        <listEntry value="WRITERS;;{w1, w2, w3};1;0"/>
        <listEntry value="LOOP_COUNT;;5;0;0"/>
        <listEntry value="TIME_TICK_UNIT;;0;0;0"/>
    </listAttribute>
    <intAttribute key="modelVersion" value="20191005"/>
    <intAttribute key="numberOfWorkers" value="12"/>
    <booleanAttribute key="recover" value="false"/>
    <stringAttribute key="result.mail.address" value=""/>
    <intAttribute key="simuAril" value="-1"/>
    <intAttribute key="simuDepth" value="100"/>
    <intAttribute key="simuSeed" value="-1"/>
    <stringAttribute key="specName" value="dynamodblock"/>
    <stringAttribute key="tlcResourcesProfile" value="local custom"/>
    <stringAttribute key="view" value=""/>
    <booleanAttribute key="visualizeStateGraph" value="false"/>
</launchConfiguration>
