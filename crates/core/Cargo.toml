[package]
name = "deltalake-core"
version = "0.18.1"
authors.workspace = true
keywords.workspace = true
readme.workspace = true
edition.workspace = true
homepage.workspace = true
description.workspace = true
license.workspace = true
repository.workspace = true
rust-version.workspace = true

[package.metadata.docs.rs]
features = ["datafusion", "json", "unity-experimental"]

[dependencies]
delta_kernel.workspace = true

# arrow
arrow = { workspace = true }
arrow-arith = { workspace = true }
arrow-array = { workspace = true , features = ["chrono-tz"]}
arrow-buffer = { workspace = true }
arrow-cast = { workspace = true }
arrow-ipc = { workspace = true }
arrow-json = { workspace = true }
arrow-ord = { workspace = true }
arrow-row = { workspace = true }
arrow-schema = { workspace = true, features = ["serde"] }
arrow-select = { workspace = true }
parquet = { workspace = true, features = [
    "async",
    "object_store",
] }
pin-project-lite = "^0.2.7"

# datafusion
datafusion = { workspace = true, optional = true }
datafusion-expr = { workspace = true, optional = true }
datafusion-common = { workspace = true, optional = true }
datafusion-proto = { workspace = true, optional = true }
datafusion-sql = { workspace = true, optional = true }
datafusion-physical-expr = { workspace = true, optional = true }
datafusion-functions = { workspace = true, optional = true }
datafusion-functions-array = { workspace = true, optional = true }

# serde
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }

# "stdlib"
bytes = { workspace = true }
chrono = { workspace = true, default-features = false, features = ["clock"] }
hashbrown = "0.14.3"
regex = { workspace = true }
thiserror = { workspace = true }
uuid = { workspace = true, features = ["serde", "v4"] }
url = { workspace = true }

# runtime
async-trait = { workspace = true }
futures = { workspace = true }
num_cpus = { workspace = true }
tokio = { workspace = true, features = [
    "macros",
    "rt",
    "rt-multi-thread",
    "sync",
    "fs",
    "parking_lot",
] }

# other deps (these should be organized and pulled into workspace.dependencies as necessary)
cfg-if = "1"
dashmap = "6"
errno = "0.3"
either = "1.8"
fix-hidden-lifetime-bug = "0.2"
hyper = { version = "0.14", optional = true }
indexmap = "2.2.1"
itertools = "0.13"
lazy_static = "1"
libc = ">=0.2.90, <1"
num-bigint = "0.4"
num-traits = "0.2.15"
object_store = { workspace = true }
once_cell = "1.16.0"
parking_lot = "0.12"
percent-encoding = "2"
roaring = "0.10.1"
tracing = { workspace = true }
rand = "0.8"
z85 = "3.0.5"
maplit = "1"

# Unity
reqwest = { version = "0.11.18", default-features = false, features = [
    "rustls-tls",
    "json",
], optional = true }
sqlparser = { version = "0.47", optional = true }

[dev-dependencies]
criterion = "0.5"
ctor = "0"
deltalake-test = { path = "../test", features = ["datafusion"] }
dotenvy = "0"
hyper = { version = "0.14", features = ["server"] }
maplit = "1"
pretty_assertions = "1.2.1"
pretty_env_logger = "0.5.0"
rand = "0.8"
serial_test = "3"
tempfile = "3"
tokio = { version = "1", features = ["macros", "rt-multi-thread"] }
utime = "0.3"

[features]
cdf = []
default = ["cdf"]
datafusion = [
    "dep:datafusion",
    "datafusion-expr",
    "datafusion-common",
    "datafusion-proto",
    "datafusion-physical-expr",
    "datafusion-sql",
    "datafusion-functions",
    "datafusion-functions-array",
    "sqlparser",
]
datafusion-ext = ["datafusion"]
json = ["parquet/json"]
python = ["arrow/pyarrow"]
unity-experimental = ["reqwest", "hyper"]
