{"type": "struct", "fields": [{"name": "txn", "type": {"type": "struct", "fields": [{"name": "appId", "type": "string", "nullable": true, "metadata": {}}, {"name": "version", "type": "long", "nullable": true, "metadata": {}}, {"name": "lastUpdated", "type": "long", "nullable": true, "metadata": {}}]}, "nullable": true, "metadata": {}}, {"name": "add", "type": {"type": "struct", "fields": [{"name": "path", "type": "string", "nullable": true, "metadata": {}}, {"name": "partitionValues", "type": {"type": "map", "keyType": "string", "valueType": "string", "valueContainsNull": true}, "nullable": true, "metadata": {}}, {"name": "size", "type": "long", "nullable": true, "metadata": {}}, {"name": "modificationTime", "type": "long", "nullable": true, "metadata": {}}, {"name": "dataChange", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "tags", "type": {"type": "map", "keyType": "string", "valueType": "string", "valueContainsNull": true}, "nullable": true, "metadata": {}}, {"name": "stats", "type": "string", "nullable": true, "metadata": {}}]}, "nullable": true, "metadata": {}}, {"name": "remove", "type": {"type": "struct", "fields": [{"name": "path", "type": "string", "nullable": true, "metadata": {}}, {"name": "deletionTimestamp", "type": "long", "nullable": true, "metadata": {}}, {"name": "dataChange", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "extendedFileMetadata", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "partitionValues", "type": {"type": "map", "keyType": "string", "valueType": "string", "valueContainsNull": true}, "nullable": true, "metadata": {}}, {"name": "size", "type": "long", "nullable": true, "metadata": {}}, {"name": "tags", "type": {"type": "map", "keyType": "string", "valueType": "string", "valueContainsNull": true}, "nullable": true, "metadata": {}}]}, "nullable": true, "metadata": {}}, {"name": "metaData", "type": {"type": "struct", "fields": [{"name": "id", "type": "string", "nullable": true, "metadata": {}}, {"name": "name", "type": "string", "nullable": true, "metadata": {}}, {"name": "description", "type": "string", "nullable": true, "metadata": {}}, {"name": "format", "type": {"type": "struct", "fields": [{"name": "provider", "type": "string", "nullable": true, "metadata": {}}, {"name": "options", "type": {"type": "map", "keyType": "string", "valueType": "string", "valueContainsNull": true}, "nullable": true, "metadata": {}}]}, "nullable": true, "metadata": {}}, {"name": "schemaString", "type": "string", "nullable": true, "metadata": {}}, {"name": "partitionColumns", "type": {"type": "array", "elementType": "string", "containsNull": true}, "nullable": true, "metadata": {}}, {"name": "configuration", "type": {"type": "map", "keyType": "string", "valueType": "string", "valueContainsNull": true}, "nullable": true, "metadata": {}}, {"name": "createdTime", "type": "long", "nullable": true, "metadata": {}}]}, "nullable": true, "metadata": {}}, {"name": "protocol", "type": {"type": "struct", "fields": [{"name": "minReaderVersion", "type": "integer", "nullable": true, "metadata": {}}, {"name": "minWriterVersion", "type": "integer", "nullable": true, "metadata": {}}]}, "nullable": true, "metadata": {}}]}