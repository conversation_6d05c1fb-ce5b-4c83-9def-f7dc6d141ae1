[package]
name = "deltalake-sql"
version = "0.1.0"
authors.workspace = true
keywords.workspace = true
readme.workspace = true
edition.workspace = true
homepage.workspace = true
description.workspace = true
license.workspace = true
repository.workspace = true
rust-version.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
datafusion-common = { workspace = true }
datafusion-expr = { workspace = true }
datafusion-sql = { workspace = true }

[dev-dependencies]
arrow-schema = { workspace = true }
