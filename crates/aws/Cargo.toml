[package]
name = "deltalake-aws"
version = "0.1.2"
authors.workspace = true
keywords.workspace = true
readme.workspace = true
edition.workspace = true
homepage.workspace = true
description.workspace = true
license.workspace = true
repository.workspace = true
rust-version.workspace = true

[dependencies]
deltalake-core = { version = ">=0.17.0, <0.19.0", path = "../core" }
aws-smithy-runtime-api = { version="1.1.7" }
aws-smithy-runtime = { version="1.1.7", optional = true}
aws-credential-types = { version="1.1.7", features = ["hardcoded-credentials"]}
aws-config = { version = "1.1.6", default-features = false, features = ["behavior-version-latest","rt-tokio", "credentials-process", "sso"] }
aws-sdk-dynamodb = {version = "1.15.0", default-features = false, features = ["behavior-version-latest", "rt-tokio"] }
aws-sdk-sts = {version = "1.1.6", default-features = false, features = ["behavior-version-latest", "rt-tokio"] }
lazy_static = "1"
maplit = "1"

# workspace dependencies
async-trait = { workspace = true }
bytes = { workspace = true }
futures = { workspace = true }
tracing = { workspace = true }
object_store = { workspace = true, features = ["aws"]}
thiserror = { workspace = true }
tokio = { workspace = true }
regex = { workspace = true }
uuid = { workspace = true, features = ["serde", "v4"] }
url = { workspace = true }
backoff = { version = "0.4", features = [ "tokio" ] }
hyper-tls = { version = "0.5", optional = true }

[dev-dependencies]
deltalake-core = { path = "../core", features = ["datafusion"] }
chrono = { workspace = true }
serial_test = "3"
deltalake-test = { path = "../test" }
pretty_env_logger = "0.5.0"
rand = "0.8"
serde_json = { workspace = true }

[features]
default = ["rustls"]
integration_test = []
native-tls = [
    "aws-config/client-hyper",
    "aws-smithy-runtime/connector-hyper-0-14-x",
    "hyper-tls"
]
rustls = [
    "aws-config/client-hyper",
    "aws-config/rustls",
    "aws-sdk-dynamodb/rustls",
    "aws-sdk-sts/rustls",
]
