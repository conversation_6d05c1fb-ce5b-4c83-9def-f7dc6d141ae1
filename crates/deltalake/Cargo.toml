[package]
name = "deltalake"
version = "0.18.1"
authors.workspace = true
keywords.workspace = true
readme.workspace = true
edition.workspace = true
homepage.workspace = true
description.workspace = true
license.workspace = true
repository.workspace = true
rust-version.workspace = true

[package.metadata.docs.rs]
# We cannot use all_features because TLS features are mutually exclusive.
features = ["azure", "datafusion", "gcs", "hdfs", "json", "python", "s3", "unity-experimental"]

[dependencies]
deltalake-core = { version = "~0.18.0", path = "../core" }
deltalake-aws = { version = "0.1.1", path = "../aws", default-features = false, optional = true }
deltalake-azure = { version = "0.1.1", path = "../azure", optional = true }
deltalake-gcp = { version = "0.2.1", path = "../gcp", optional = true }
deltalake-hdfs = { version = "0.1.0", path = "../hdfs", optional = true }
deltalake-catalog-glue = { version = "0.1.0", path = "../catalog-glue", optional = true }

[features]
# All of these features are just reflected into the core crate until that
# functionality is broken apart
azure = ["deltalake-azure"]
default = []
datafusion = ["deltalake-core/datafusion"]
datafusion-ext = ["datafusion"]
gcs = ["deltalake-gcp"]
glue = ["deltalake-catalog-glue"]
hdfs = ["deltalake-hdfs"]
json = ["deltalake-core/json"]
python = ["deltalake-core/python"]
s3-native-tls = ["deltalake-aws/native-tls"]
s3 = ["deltalake-aws/rustls"]
unity-experimental = ["deltalake-core/unity-experimental"]

[dev-dependencies]
tokio = { version = "1", features = ["macros", "rt-multi-thread"] }
chrono = { workspace = true, default-features = false, features = ["clock"] }
tracing = { workspace = true }

[[example]]
name = "basic_operations"
required-features = ["datafusion"]

[[example]]
name = "load_table"
required-features = ["datafusion"]

[[example]]
name = "recordbatch-writer"
