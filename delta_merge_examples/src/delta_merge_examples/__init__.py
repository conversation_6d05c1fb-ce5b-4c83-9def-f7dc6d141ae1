"""
Delta Lake merge examples package.

This package demonstrates various approaches to replacing data in Delta tables,
particularly for ETL scenarios where new versions of files need to replace
existing data.
"""

from .merge_examples import DeltaMergeExamples, run_all_examples
from .comparison import MergeVsDeleteAppendComparison, run_comparison
from .acid_analysis import ACIDAnalysis, run_acid_analysis
from .scale_analysis import ScaleAnalysis, run_scale_analysis
from .data_generator import (
    create_initial_table_data,
    create_file_replacement_same_rows,
    create_file_replacement_fewer_rows,
    create_file_replacement_more_rows,
    create_empty_file_replacement,
    create_large_file_data,
    print_table_summary
)

__version__ = "0.1.0"
__all__ = [
    "DeltaMergeExamples",
    "run_all_examples",
    "MergeVsDeleteAppendComparison",
    "run_comparison",
    "ACIDAnalysis",
    "run_acid_analysis",
    "ScaleAnalysis",
    "run_scale_analysis",
    "create_initial_table_data",
    "create_file_replacement_same_rows",
    "create_file_replacement_fewer_rows",
    "create_file_replacement_more_rows",
    "create_empty_file_replacement",
    "create_large_file_data",
    "print_table_summary"
]
